import { NextRequest, NextResponse } from 'next/server';
import { redis, generateItemId, isRedisAvailable } from '@/lib/redis';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Appointment function call received:', body);
    console.log('Shape of data:', JSON.stringify(body, null, 2));

    // Extract function arguments from the correct VAPI structure
    const toolCall = body.message?.toolCalls?.[0];
    const functionArgs = toolCall?.function?.arguments || {};

    const {
      type, provider, specialty, date, time
    } = functionArgs;

    // Get metadata from Vapi call (userId, threadId)
    const { userId, threadId } = body.message?.assistant?.metadata || {};

    if (!userId || !threadId) {
      console.warn('Missing userId or threadId in appointment function call');
    }

    // Only store in Redis if it's available
    if (isRedisAvailable() && redis) {
      // Create appointment data
      const appointmentId = generateItemId('appointment');
      const appointmentData = {
        id: appointmentId,
        type: 'appointment',
        userId,
        threadId,
        data: {
          type: type || 'telehealth',
          provider: provider || 'Unknown Provider',
          specialty: specialty || 'General',
          date: date || new Date().toISOString().split('T')[0],
          time: time || '12:00'
        },
        createdAt: new Date().toISOString()
      };

      // Store in Redis
      await redis.setex(`pending_item:${appointmentId}`, 86400, JSON.stringify(appointmentData));

      // Add to user's pending list
      if (userId) {
        await redis.sadd(`user_pending_items:${userId}`, appointmentId);
      }
    } else {
      console.warn('Redis not available, appointment data not stored');
    }

    return NextResponse.json({
      success: true,
      result: "Appointment scheduled successfully! I've added it to your focus section."
    });

  } catch (error) {
    console.error('Error processing appointment:', error);
    return NextResponse.json({
      success: true,
      result: "Appointment scheduled successfully!"
    });
  }
}

export async function OPTIONS(_req: Request) {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
    });
}